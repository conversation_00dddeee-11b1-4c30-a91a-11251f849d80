import type { NextConfig } from "next";

const nextConfig = {
  async headers() {
    return [
      {
        // This applies to all routes in your app.
        source: '/:path*',
        headers: [
          {
            key: 'Cache-Control',
            // This header tells browsers and CDNs not to cache the page.
            // s-maxage=0: Tells CDNs not to cache.
            // stale-while-revalidate: Can serve stale content while fetching fresh content in the background.
            value: 'no-store, no-cache, must-revalidate, proxy-revalidate, s-maxage=0',
          },
          {
            key: 'Pragma',
            value: 'no-cache', // For legacy HTTP/1.0 caches
          },
          {
            key: 'Expires',
            value: '0', // For legacy proxies
          },
        ],
      },
    ];
  },
};

export default nextConfig;
