import Image from 'next/image';
import Link from 'next/link';

export const CTASection = () => {
  return (
    // Use a relative container to position the decorative shapes
    <section className="relative bg-[#F3F9F9] py-20 lg:py-24 overflow-hidden ">
      {/* Top-left decorative shape */}
      <div className="absolute top-0 left-0 hidden sm:block">
        <Image
          src="/tl.svg" // Assuming tl.svg is in your /public folder
          alt="Top-left decorative graphic"
          width={250}
          height={250}
        />
      </div>

      {/* +++ ADDED: Bottom-right decorative image +++ */}
      <div className="absolute bottom-0 right-0 hidden sm:block">
        <Image
          src="/br.svg" // Assuming br.svg is in your /public folder
          alt="Bottom-right decorative graphic"
          width={250}
          height={250}
        />
      </div>


      {/* Content */}
      <div className="container mx-auto px-4 lg:px-8 text-center relative z-10">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl  font-extrabold text-gray-900 mb-5">
            Get Paid Faster with Invoice Discounting
          </h2>
          
          <p className="text-base text-black mb-10 leading-relaxed">
            Unlock cash tied up in your unpaid invoices—no more waiting months for buyer payments. 
            With our seamless, tech-powered platform, you can turn your invoices into instant cash. 
            Get credit line offers from multiple trusted lenders. Upload your invoice, and get funded.
          </p>

          <div className='hover:scale-110 duration-100'>
            <Link 
              href="https://madad-msme.fundfina.com" 
              className="bg-[#5DAE62] text-white px-10 py-3.5 rounded-lg font-semibold transition-colors text-lg shadow-md hover:shadow-lg "
            >
              Get Started
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};
