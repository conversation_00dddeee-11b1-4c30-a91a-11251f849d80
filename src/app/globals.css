@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@300;400;600;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
  --dark-green: #004141;
  --light-green: #1B9D51;
  --primary: #5DAE62;
  --secondary: #003A3A;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-dark-green: var(--dark-green);
  --color-light-green: var(--light-green);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --font-sans: 'Nunito Sans', sans-serif;
  --font-mono: var(--font-geist-mono);
}

* {
  font-family: 'Nunito Sans', sans-serif !important;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Nunito Sans', sans-serif;
  width: 100% !important;
  height: 100% !important;
  overflow-x: hidden !important;
}

/* Custom utility classes */
.text-dark-green {
  color: var(--dark-green);
}

.bg-dark-green {
  background-color: var(--dark-green);
}

.text-light-green {
  color: var(--light-green);
}

.bg-light-green {
  background-color: var(--light-green);
}

/* Main container */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header styles */
header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: white;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}

/* Mobile menu transition */
#mobile-menu {
  transition: max-height 0.3s ease;
}

/* Hamburger button */
#hamburger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Navigation styles */
.nav-link {
  color: var(--dark-green);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.nav-link:hover {
  text-decoration: underline;
}

.nav-button {
  background-color: var(--dark-green);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.nav-button:hover {
  background-color: #003333;
}

/* Logo styles */
.logo_top {
  height: 40px;
  width: auto;
}

/* Hero section */
.hero-section {
  padding: 60px 0;
}

.hero-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

@media (max-width: 768px) {
  .hero-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .hero-image {
    order: -1;
  }
}

.hero-text h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-green);
  margin-bottom: 20px;
  line-height: 1.2;
}

.waiting-paragraph {
  font-size: 1.375rem;
  color: #666;
  margin-bottom: 10px;
}

.wait-no-more {
  font-size: 1.125rem;
  color: #333;
  margin-bottom: 30px;
}

.coming_soon_in_qatar {
  color: var(--dark-green);
  font-weight: 600;
  font-size: 1.125rem;
}

/* Form styles */
.form-container-hero {
  margin-bottom: 20px;
}

.input_email_submit_button {
  background-color: var(--dark-green);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 16px;
}

.input_email_submit_button:hover {
  background-color: #003333;
}

.form_error_message {
  color: #dc2626;
  font-size: 14px;
  margin-top: 10px;
}

/* Features section */
.features_container {
  padding: 80px 0;
  background-color: #f9f9f9;
}

.features_container h1 {
  text-align: center;
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--dark-green);
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.feature-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.icon-wrapper {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
}

.icon-wrapper img {
  max-height: 60px;
  width: auto;
}

.QAR {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-green);
  background-color: #f0f0f0;
  padding: 15px 20px;
  border-radius: 10px;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--dark-green);
  margin-bottom: 15px;
}

.feature-description {
  color: #666;
  line-height: 1.6;
}

/* Process section */
.section-3 {
  padding: 80px 0;
  background: white;
}

.section3_main_container .title {
  text-align: center;
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--dark-green);
  margin-bottom: 60px;
}

.process {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.line-container {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  transform: translateX(-50%);
}

.dashed-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  border-left: 2px dashed #ddd;
}

.solid-line {
  position: absolute;
  top: 0;
  width: 100%;
  background-color: var(--light-green);
  transition: height 0.3s ease;
  height: 0;
}

.arrow {
  position: absolute;
  top: 0;
  left: -5px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 10px solid var(--light-green);
  transition: top 0.3s ease;
}

.step {
  position: relative;
  margin-bottom: 60px;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.step.active {
  opacity: 1;
}

.step:nth-child(even) .step-content {
  flex-direction: row-reverse;
}

.circle {
  position: absolute;
  left: 50%;
  top: 20px;
  width: 20px;
  height: 20px;
  background-color: var(--light-green);
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.step-title {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark-green);
  margin-bottom: 30px;
}

.step-content {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.step-left,
.step-right {
  flex: 1;
}

.subtitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--dark-green);
  margin-bottom: 15px;
}

.list {
  list-style: none;
  padding: 0;
  margin-bottom: 20px;
}

.list li {
  padding: 5px 0;
  color: #666;
}

.list li:before {
  content: "✓ ";
  color: var(--light-green);
  font-weight: bold;
  margin-right: 8px;
}

.benefits p {
  background-color: #f0f8f0;
  padding: 8px 12px;
  border-radius: 5px;
  margin-bottom: 8px;
  color: var(--dark-green);
  font-weight: 600;
  font-size: 0.875rem;
}

/* Invoice discounting section */
.invoice-discounting-container {
  padding: 60px 0;
  text-align: center;
}

.invoice-discounting-container h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-green);
  margin-bottom: 30px;
}

.invoice-discounting-container p {
  font-size: 1.125rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* FAQ section */
.FAQs {
  padding: 60px 0;
}

.FAQs h1 {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-green);
  margin-bottom: 40px;
}

.accordion {
  max-width: 800px;
  margin: 0 auto;
}

.accordion-item {
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin-bottom: 10px;
  overflow: hidden;
}

.accordion-header {
  width: 100%;
  padding: 20px;
  background: white;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--dark-green);
  transition: background-color 0.3s ease;
}

.accordion-header:hover {
  background-color: #f9f9f9;
}

.accordion-icon {
  transition: transform 0.3s ease;
}

.accordion-item.active .accordion-icon {
  transform: rotate(180deg);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: #f9f9f9;
}

.accordion-item.active .accordion-content {
  max-height: 200px;
}

.accordion-content p {
  padding: 20px;
  margin: 0;
  color: #666;
  line-height: 1.6;
}

/* Contact section */
#contact {
  background-color: white;
  padding: 40px 20px;
  text-align: center;
}

#contact h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark-green);
  margin-bottom: 30px;
}

/* Footer */
.footer-page-body {
  background-color: #f9f9f9;
  padding: 40px 0;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.footer-address p {
  color: #666;
  margin: 5px 0;
}

.footer-social {
  display: flex;
  gap: 15px;
  align-items: center;
}

.footer-social img {
  width: 30px;
  height: 30px;
  transition: opacity 0.3s ease;
}

.footer-social img:hover {
  opacity: 0.7;
}

.footer-copyright {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ddd;
}

.footer-copyright p {
  color: #666;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .main-container {
    padding: 0 15px;
  }

  .hero-text h1 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .step-content {
    flex-direction: column;
    gap: 20px;
  }

  .step:nth-child(even) .step-content {
    flex-direction: column;
  }

  .footer-info {
    flex-direction: column;
    text-align: center;
  }
}
